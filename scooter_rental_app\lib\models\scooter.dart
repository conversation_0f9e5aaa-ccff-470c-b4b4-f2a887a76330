enum ScooterStatus { available, inUse, maintenance, offline }

class Scooter {
  final int id;
  final String code;
  final double latitude;
  final double longitude;
  final int batteryLevel;
  final ScooterStatus status;
  final double pricePerMinute;
  final String? model;
  final DateTime createdAt;
  final DateTime updatedAt;

  Scooter({
    required this.id,
    required this.code,
    required this.latitude,
    required this.longitude,
    required this.batteryLevel,
    required this.status,
    required this.pricePerMinute,
    this.model,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Scooter.fromJson(Map<String, dynamic> json) {
    return Scooter(
      id: json['id'],
      code: json['code'],
      latitude: double.parse(json['latitude'].toString()),
      longitude: double.parse(json['longitude'].toString()),
      batteryLevel: json['battery_level'],
      status: _statusFromString(json['status']),
      pricePerMinute: double.parse(json['price_per_minute'].toString()),
      model: json['model'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'latitude': latitude,
      'longitude': longitude,
      'battery_level': batteryLevel,
      'status': _statusToString(status),
      'price_per_minute': pricePerMinute,
      'model': model,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  static ScooterStatus _statusFromString(String status) {
    switch (status.toLowerCase()) {
      case 'available':
        return ScooterStatus.available;
      case 'in_use':
        return ScooterStatus.inUse;
      case 'maintenance':
        return ScooterStatus.maintenance;
      case 'offline':
        return ScooterStatus.offline;
      default:
        return ScooterStatus.offline;
    }
  }

  static String _statusToString(ScooterStatus status) {
    switch (status) {
      case ScooterStatus.available:
        return 'available';
      case ScooterStatus.inUse:
        return 'in_use';
      case ScooterStatus.maintenance:
        return 'maintenance';
      case ScooterStatus.offline:
        return 'offline';
    }
  }

  Scooter copyWith({
    int? id,
    String? code,
    double? latitude,
    double? longitude,
    int? batteryLevel,
    ScooterStatus? status,
    double? pricePerMinute,
    String? model,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Scooter(
      id: id ?? this.id,
      code: code ?? this.code,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      status: status ?? this.status,
      pricePerMinute: pricePerMinute ?? this.pricePerMinute,
      model: model ?? this.model,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isAvailable => status == ScooterStatus.available;

  String get statusDisplayName {
    switch (status) {
      case ScooterStatus.available:
        return 'Available';
      case ScooterStatus.inUse:
        return 'In Use';
      case ScooterStatus.maintenance:
        return 'Maintenance';
      case ScooterStatus.offline:
        return 'Offline';
    }
  }

  @override
  String toString() {
    return 'Scooter{id: $id, code: $code, status: $status, battery: $batteryLevel%}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Scooter && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
