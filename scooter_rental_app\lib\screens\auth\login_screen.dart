import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/constants.dart';
import '../../utils/routes.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/loading_indicator.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;
  bool _rememberMe = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    final success = await authProvider.login(
      email: _emailController.text.trim(),
      password: _passwordController.text,
    );

    if (!mounted) return;

    if (success) {
      AppRoutes.pushAndRemoveUntil(context, AppRoutes.home);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(authProvider.errorMessage ?? 'Login failed'),
          backgroundColor: Constants.errorColor,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            return LoadingOverlay(
              isLoading: authProvider.isLoading,
              loadingMessage: 'Signing in...',
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(Constants.defaultPadding),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: Constants.largePadding * 2),
                      // Logo and Title
                      Center(
                        child: Column(
                          children: [
                            Container(
                              width: 80,
                              height: 80,
                              decoration: const BoxDecoration(
                                color: Constants.primaryColor,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.electric_scooter,
                                size: 40,
                                color: Constants.onPrimaryColor,
                              ),
                            ),
                            const SizedBox(height: Constants.defaultPadding),
                            const Text(
                              'Welcome Back',
                              style: TextStyle(
                                fontSize: Constants.headlineMediumSize,
                                fontWeight: FontWeight.bold,
                                color: Constants.textPrimaryColor,
                              ),
                            ),
                            const SizedBox(height: Constants.smallPadding),
                            Text(
                              'Sign in to continue your journey',
                              style: TextStyle(
                                fontSize: Constants.bodyLargeSize,
                                color: Constants.textSecondaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: Constants.largePadding * 2),
                      // Email Field
                      TextFormField(
                        controller: _emailController,
                        keyboardType: TextInputType.emailAddress,
                        textInputAction: TextInputAction.next,
                        decoration: const InputDecoration(
                          labelText: 'Email',
                          hintText: 'Enter your email',
                          prefixIcon: Icon(Icons.email_outlined),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return Constants.emailRequiredMessage;
                          }
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                              .hasMatch(value)) {
                            return Constants.emailInvalidMessage;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: Constants.defaultPadding),
                      // Password Field
                      TextFormField(
                        controller: _passwordController,
                        obscureText: _obscurePassword,
                        textInputAction: TextInputAction.done,
                        decoration: InputDecoration(
                          labelText: 'Password',
                          hintText: 'Enter your password',
                          prefixIcon: const Icon(Icons.lock_outlined),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _obscurePassword
                                  ? Icons.visibility_outlined
                                  : Icons.visibility_off_outlined,
                            ),
                            onPressed: () {
                              setState(() {
                                _obscurePassword = !_obscurePassword;
                              });
                            },
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return Constants.passwordRequiredMessage;
                          }
                          return null;
                        },
                        onFieldSubmitted: (_) => _handleLogin(),
                      ),
                      const SizedBox(height: Constants.defaultPadding),
                      // Remember Me and Forgot Password
                      Row(
                        children: [
                          Checkbox(
                            value: _rememberMe,
                            onChanged: (value) {
                              setState(() {
                                _rememberMe = value ?? false;
                              });
                            },
                          ),
                          const Text('Remember me'),
                          const Spacer(),
                          TextButton(
                            onPressed: () {
                              // TODO: Implement forgot password
                            },
                            child: const Text('Forgot Password?'),
                          ),
                        ],
                      ),
                      const SizedBox(height: Constants.largePadding),
                      // Login Button
                      CustomButton(
                        text: 'Sign In',
                        onPressed: _handleLogin,
                        isLoading: authProvider.isLoading,
                      ),
                      const SizedBox(height: Constants.defaultPadding),
                      // Divider
                      Row(
                        children: [
                          const Expanded(child: Divider()),
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: Constants.defaultPadding,
                            ),
                            child: Text(
                              'OR',
                              style: TextStyle(
                                color: Constants.textSecondaryColor,
                                fontSize: Constants.bodySmallSize,
                              ),
                            ),
                          ),
                          const Expanded(child: Divider()),
                        ],
                      ),
                      const SizedBox(height: Constants.defaultPadding),
                      // Register Button
                      CustomButton(
                        text: 'Create Account',
                        type: ButtonType.outline,
                        onPressed: () {
                          AppRoutes.push(context, AppRoutes.register);
                        },
                      ),
                      const SizedBox(height: Constants.largePadding),
                      // Terms and Privacy
                      Center(
                        child: Wrap(
                          alignment: WrapAlignment.center,
                          children: [
                            Text(
                              'By continuing, you agree to our ',
                              style: TextStyle(
                                fontSize: Constants.bodySmallSize,
                                color: Constants.textSecondaryColor,
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                // TODO: Show terms
                              },
                              child: Text(
                                'Terms of Service',
                                style: TextStyle(
                                  fontSize: Constants.bodySmallSize,
                                  color: Constants.primaryColor,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                            Text(
                              ' and ',
                              style: TextStyle(
                                fontSize: Constants.bodySmallSize,
                                color: Constants.textSecondaryColor,
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                // TODO: Show privacy policy
                              },
                              child: Text(
                                'Privacy Policy',
                                style: TextStyle(
                                  fontSize: Constants.bodySmallSize,
                                  color: Constants.primaryColor,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
