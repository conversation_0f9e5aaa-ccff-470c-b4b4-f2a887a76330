import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/rides_provider.dart';
import '../providers/scooters_provider.dart';
import '../utils/constants.dart';
import '../utils/routes.dart';
import '../widgets/custom_button.dart';
import '../widgets/loading_indicator.dart';

class RideScreen extends StatefulWidget {
  const RideScreen({Key? key}) : super(key: key);

  @override
  State<RideScreen> createState() => _RideScreenState();
}

class _RideScreenState extends State<RideScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Current Ride'),
        backgroundColor: Constants.accentColor,
        foregroundColor: Constants.onPrimaryColor,
      ),
      body: Consumer<RidesProvider>(
        builder: (context, ridesProvider, child) {
          final currentRide = ridesProvider.currentRide;

          if (currentRide == null) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.electric_scooter_outlined,
                    size: 64,
                    color: Constants.textSecondaryColor,
                  ),
                  SizedBox(height: Constants.defaultPadding),
                  Text(
                    'No Active Ride',
                    style: TextStyle(
                      fontSize: Constants.titleLargeSize,
                      color: Constants.textSecondaryColor,
                    ),
                  ),
                  SizedBox(height: Constants.smallPadding),
                  Text(
                    'Start a ride to see details here',
                    style: TextStyle(
                      fontSize: Constants.bodyMediumSize,
                      color: Constants.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            );
          }

          return LoadingOverlay(
            isLoading: ridesProvider.isLoading,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(Constants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Ride Status Card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(Constants.defaultPadding),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 12,
                                height: 12,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Constants.accentColor,
                                ),
                              ),
                              const SizedBox(width: Constants.smallPadding),
                              const Text(
                                'Ride Active',
                                style: TextStyle(
                                  fontSize: Constants.titleMediumSize,
                                  fontWeight: FontWeight.bold,
                                  color: Constants.accentColor,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: Constants.defaultPadding),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              _buildStatItem(
                                'Duration',
                                ridesProvider.getFormattedCurrentRideDuration(),
                                Icons.timer,
                              ),
                              _buildStatItem(
                                'Distance',
                                ridesProvider.getFormattedCurrentRideDistance() ?? 'N/A',
                                Icons.straighten,
                              ),
                              _buildStatItem(
                                'Est. Cost',
                                '\$${ridesProvider.estimateCurrentRideCost(currentRide.scooter?.pricePerMinute ?? 0.25)?.toStringAsFixed(2) ?? '0.00'}',
                                Icons.attach_money,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: Constants.defaultPadding),

                  // Scooter Info Card
                  if (currentRide.scooter != null) ...[
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(Constants.defaultPadding),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Scooter Details',
                              style: TextStyle(
                                fontSize: Constants.titleMediumSize,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: Constants.defaultPadding),
                            Row(
                              children: [
                                const Icon(Icons.electric_scooter),
                                const SizedBox(width: Constants.smallPadding),
                                Text(
                                  'Scooter ${currentRide.scooter!.code}',
                                  style: const TextStyle(
                                    fontSize: Constants.bodyLargeSize,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: Constants.smallPadding),
                            Row(
                              children: [
                                const Icon(Icons.battery_std, size: 16),
                                const SizedBox(width: 4),
                                Text('${currentRide.scooter!.batteryLevel}%'),
                                const SizedBox(width: Constants.defaultPadding),
                                const Icon(Icons.attach_money, size: 16),
                                Text('\$${currentRide.scooter!.pricePerMinute.toStringAsFixed(2)}/min'),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: Constants.defaultPadding),
                  ],

                  // Map Placeholder
                  Card(
                    child: Container(
                      height: 200,
                      padding: const EdgeInsets.all(Constants.defaultPadding),
                      child: const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.map,
                              size: 48,
                              color: Constants.textSecondaryColor,
                            ),
                            SizedBox(height: Constants.smallPadding),
                            Text(
                              'Live Map Tracking',
                              style: TextStyle(
                                fontSize: Constants.bodyLargeSize,
                                color: Constants.textSecondaryColor,
                              ),
                            ),
                            Text(
                              'Real-time location tracking would appear here',
                              style: TextStyle(
                                fontSize: Constants.bodySmallSize,
                                color: Constants.textSecondaryColor,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: Constants.largePadding),

                  // End Ride Button
                  CustomButton(
                    text: 'End Ride',
                    onPressed: () => _showEndRideDialog(context),
                    backgroundColor: Constants.errorColor,
                    isLoading: ridesProvider.isLoading,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 24,
          color: Constants.primaryColor,
        ),
        const SizedBox(height: Constants.smallPadding),
        Text(
          value,
          style: const TextStyle(
            fontSize: Constants.titleMediumSize,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: Constants.bodySmallSize,
            color: Constants.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  void _showEndRideDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('End Ride'),
        content: const Text('Are you sure you want to end this ride?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _endRide();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Constants.errorColor,
            ),
            child: const Text('End Ride'),
          ),
        ],
      ),
    );
  }

  Future<void> _endRide() async {
    final ridesProvider = Provider.of<RidesProvider>(context, listen: false);
    final scootersProvider = Provider.of<ScootersProvider>(context, listen: false);

    final success = await ridesProvider.endRide();

    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(Constants.rideEndedMessage),
          backgroundColor: Constants.successColor,
        ),
      );

      // Refresh scooters
      await scootersProvider.refreshScooters();

      // Navigate back to home
      AppRoutes.pop(context);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(ridesProvider.errorMessage ?? 'Failed to end ride'),
          backgroundColor: Constants.errorColor,
        ),
      );
    }
  }
}
