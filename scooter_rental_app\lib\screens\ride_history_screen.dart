import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/rides_provider.dart';
import '../utils/constants.dart';
import '../widgets/loading_indicator.dart';

class RideHistoryScreen extends StatefulWidget {
  const RideHistoryScreen({Key? key}) : super(key: key);

  @override
  State<RideHistoryScreen> createState() => _RideHistoryScreenState();
}

class _RideHistoryScreenState extends State<RideHistoryScreen> {
  @override
  void initState() {
    super.initState();
    _loadRideHistory();
  }

  Future<void> _loadRideHistory() async {
    final ridesProvider = Provider.of<RidesProvider>(context, listen: false);
    await ridesProvider.loadRideHistory();
  }

  Future<void> _refreshRideHistory() async {
    await _loadRideHistory();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ride History'),
      ),
      body: Consumer<RidesProvider>(
        builder: (context, ridesProvider, child) {
          if (ridesProvider.isLoading) {
            return const LoadingIndicator(message: 'Loading ride history...');
          }

          final rides = ridesProvider.rideHistory;

          if (rides.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.history,
                    size: 64,
                    color: Constants.textSecondaryColor,
                  ),
                  SizedBox(height: Constants.defaultPadding),
                  Text(
                    'No Ride History',
                    style: TextStyle(
                      fontSize: Constants.titleLargeSize,
                      color: Constants.textSecondaryColor,
                    ),
                  ),
                  SizedBox(height: Constants.smallPadding),
                  Text(
                    'Your completed rides will appear here',
                    style: TextStyle(
                      fontSize: Constants.bodyMediumSize,
                      color: Constants.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Statistics Card
              Card(
                margin: const EdgeInsets.all(Constants.defaultPadding),
                child: Padding(
                  padding: const EdgeInsets.all(Constants.defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Your Stats',
                        style: TextStyle(
                          fontSize: Constants.titleMediumSize,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: Constants.defaultPadding),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildStatItem(
                            'Total Rides',
                            ridesProvider.getTotalRides().toString(),
                            Icons.electric_scooter,
                          ),
                          _buildStatItem(
                            'Total Spent',
                            '\$${ridesProvider.getTotalSpent().toStringAsFixed(2)}',
                            Icons.attach_money,
                          ),
                          _buildStatItem(
                            'Total Time',
                            _formatDuration(ridesProvider.getTotalRideTime()),
                            Icons.timer,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Ride List
              Expanded(
                child: RefreshIndicator(
                  onRefresh: _refreshRideHistory,
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(
                      horizontal: Constants.defaultPadding,
                    ),
                    itemCount: rides.length,
                    itemBuilder: (context, index) {
                      final ride = rides[index];
                      return Card(
                        margin: const EdgeInsets.only(
                          bottom: Constants.smallPadding,
                        ),
                        child: ListTile(
                          leading: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _getRideStatusColor(ride.status.name),
                            ),
                            child: Icon(
                              _getRideStatusIcon(ride.status.name),
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          title: Text(
                            'Scooter ${ride.scooterCode ?? 'Unknown'}',
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 4),
                              Text(
                                _formatDate(ride.startTime),
                                style: const TextStyle(
                                  fontSize: Constants.bodySmallSize,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Row(
                                children: [
                                  Icon(
                                    Icons.timer,
                                    size: 14,
                                    color: Constants.textSecondaryColor,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    ride.formattedDuration,
                                    style: const TextStyle(
                                      fontSize: Constants.bodySmallSize,
                                      color: Constants.textSecondaryColor,
                                    ),
                                  ),
                                  const SizedBox(width: Constants.defaultPadding),
                                  if (ride.totalCost != null) ...[
                                    Icon(
                                      Icons.attach_money,
                                      size: 14,
                                      color: Constants.textSecondaryColor,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      '\$${ride.totalCost!.toStringAsFixed(2)}',
                                      style: const TextStyle(
                                        fontSize: Constants.bodySmallSize,
                                        color: Constants.textSecondaryColor,
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ],
                          ),
                          trailing: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: Constants.smallPadding,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: _getRideStatusColor(ride.status.name).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              ride.statusDisplayName,
                              style: TextStyle(
                                fontSize: Constants.bodySmallSize,
                                color: _getRideStatusColor(ride.status.name),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          onTap: () {
                            _showRideDetails(ride);
                          },
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 24,
          color: Constants.primaryColor,
        ),
        const SizedBox(height: Constants.smallPadding),
        Text(
          value,
          style: const TextStyle(
            fontSize: Constants.titleMediumSize,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: Constants.bodySmallSize,
            color: Constants.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  Color _getRideStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Constants.successColor;
      case 'active':
        return Constants.accentColor;
      case 'cancelled':
        return Constants.errorColor;
      default:
        return Constants.textSecondaryColor;
    }
  }

  IconData _getRideStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Icons.check;
      case 'active':
        return Icons.electric_scooter;
      case 'cancelled':
        return Icons.close;
      default:
        return Icons.help;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    }
    return '${minutes}m';
  }

  void _showRideDetails(ride) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        padding: const EdgeInsets.all(Constants.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Ride Details',
              style: const TextStyle(
                fontSize: Constants.titleLargeSize,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: Constants.defaultPadding),
            _buildDetailRow('Scooter', ride.scooterCode ?? 'Unknown'),
            _buildDetailRow('Status', ride.statusDisplayName),
            _buildDetailRow('Start Time', _formatDate(ride.startTime)),
            if (ride.endTime != null)
              _buildDetailRow('End Time', _formatDate(ride.endTime!)),
            _buildDetailRow('Duration', ride.formattedDuration),
            if (ride.totalCost != null)
              _buildDetailRow('Total Cost', '\$${ride.totalCost!.toStringAsFixed(2)}'),
            const SizedBox(height: Constants.defaultPadding),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: Constants.smallPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: Constants.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
