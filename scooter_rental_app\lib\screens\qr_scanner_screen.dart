import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../utils/routes.dart';

class QRScannerScreen extends StatefulWidget {
  const QRScannerScreen({Key? key}) : super(key: key);

  @override
  State<QRScannerScreen> createState() => _QRScannerScreenState();
}

class _QRScannerScreenState extends State<QRScannerScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan QR Code'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Container(
        color: Colors.black,
        child: Column(
          children: [
            Expanded(
              flex: 4,
              child: Container(
                width: double.infinity,
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.qr_code_scanner,
                        size: 100,
                        color: Colors.white,
                      ),
                      SizedBox(height: Constants.defaultPadding),
                      Text(
                        'QR Scanner',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: Constants.titleLargeSize,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: Constants.smallPadding),
                      Text(
                        'QR Code Scanner integration would go here',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: Constants.bodyMediumSize,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Expanded(
              flex: 1,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(Constants.defaultPadding),
                child: Column(
                  children: [
                    const Text(
                      'Point your camera at the QR code on the scooter',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: Constants.bodyLargeSize,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: Constants.defaultPadding),
                    ElevatedButton(
                      onPressed: () {
                        // Simulate QR code scan for demo
                        _simulateQRScan();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Constants.primaryColor,
                        foregroundColor: Constants.onPrimaryColor,
                      ),
                      child: const Text('Simulate Scan (Demo)'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _simulateQRScan() {
    // Simulate finding a scooter
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Scooter Found!'),
        content: const Text('Scooter SC0001 found. Would you like to start a ride?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              AppRoutes.pop(context);
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              AppRoutes.pop(context);
              // In a real app, this would start the ride with the scanned scooter
            },
            child: const Text('Start Ride'),
          ),
        ],
      ),
    );
  }
}
