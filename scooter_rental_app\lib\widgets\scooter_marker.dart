import 'package:flutter/material.dart';
import '../models/scooter.dart';
import '../utils/constants.dart';

class ScooterMarker extends StatelessWidget {
  final Scooter scooter;
  final VoidCallback? onTap;
  final bool isSelected;
  final double size;

  const ScooterMarker({
    Key? key,
    required this.scooter,
    this.onTap,
    this.isSelected = false,
    this.size = 40.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: _getMarkerColor(),
          border: Border.all(
            color: isSelected ? Constants.primaryColor : Colors.white,
            width: isSelected ? 3 : 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Icon(
            _getMarkerIcon(),
            color: Colors.white,
            size: size * 0.5,
          ),
        ),
      ),
    );
  }

  Color _getMarkerColor() {
    switch (scooter.status) {
      case ScooterStatus.available:
        return Constants.scooterAvailableColor;
      case ScooterStatus.inUse:
        return Constants.scooterInUseColor;
      case ScooterStatus.maintenance:
        return Constants.scooterMaintenanceColor;
      case ScooterStatus.offline:
        return Constants.scooterOfflineColor;
    }
  }

  IconData _getMarkerIcon() {
    switch (scooter.status) {
      case ScooterStatus.available:
        return Icons.electric_scooter;
      case ScooterStatus.inUse:
        return Icons.electric_scooter;
      case ScooterStatus.maintenance:
        return Icons.build;
      case ScooterStatus.offline:
        return Icons.power_off;
    }
  }
}

class ScooterInfoCard extends StatelessWidget {
  final Scooter scooter;
  final String? distance;
  final VoidCallback? onStartRide;
  final VoidCallback? onViewDetails;
  final bool showActions;

  const ScooterInfoCard({
    Key? key,
    required this.scooter,
    this.distance,
    this.onStartRide,
    this.onViewDetails,
    this.showActions = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(Constants.smallPadding),
      child: Padding(
        padding: const EdgeInsets.all(Constants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _getStatusColor(),
                  ),
                ),
                const SizedBox(width: Constants.smallPadding),
                Text(
                  'Scooter ${scooter.code}',
                  style: const TextStyle(
                    fontSize: Constants.titleMediumSize,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (distance != null)
                  Text(
                    distance!,
                    style: const TextStyle(
                      fontSize: Constants.bodySmallSize,
                      color: Constants.textSecondaryColor,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: Constants.smallPadding),
            Row(
              children: [
                Icon(
                  Icons.battery_std,
                  size: 16,
                  color: _getBatteryColor(),
                ),
                const SizedBox(width: 4),
                Text(
                  '${scooter.batteryLevel}%',
                  style: TextStyle(
                    fontSize: Constants.bodySmallSize,
                    color: _getBatteryColor(),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: Constants.defaultPadding),
                Icon(
                  Icons.attach_money,
                  size: 16,
                  color: Constants.textSecondaryColor,
                ),
                Text(
                  '\$${scooter.pricePerMinute.toStringAsFixed(2)}/min',
                  style: const TextStyle(
                    fontSize: Constants.bodySmallSize,
                    color: Constants.textSecondaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: Constants.smallPadding),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: Constants.smallPadding,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: _getStatusColor().withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                scooter.statusDisplayName,
                style: TextStyle(
                  fontSize: Constants.bodySmallSize,
                  color: _getStatusColor(),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if (showActions && scooter.isAvailable) ...[
              const SizedBox(height: Constants.defaultPadding),
              Row(
                children: [
                  if (onViewDetails != null)
                    Expanded(
                      child: OutlinedButton(
                        onPressed: onViewDetails,
                        child: const Text('Details'),
                      ),
                    ),
                  if (onViewDetails != null && onStartRide != null)
                    const SizedBox(width: Constants.smallPadding),
                  if (onStartRide != null)
                    Expanded(
                      child: ElevatedButton(
                        onPressed: onStartRide,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Constants.accentColor,
                        ),
                        child: const Text('Start Ride'),
                      ),
                    ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (scooter.status) {
      case ScooterStatus.available:
        return Constants.scooterAvailableColor;
      case ScooterStatus.inUse:
        return Constants.scooterInUseColor;
      case ScooterStatus.maintenance:
        return Constants.scooterMaintenanceColor;
      case ScooterStatus.offline:
        return Constants.scooterOfflineColor;
    }
  }

  Color _getBatteryColor() {
    if (scooter.batteryLevel >= 50) {
      return Constants.successColor;
    } else if (scooter.batteryLevel >= 20) {
      return Constants.warningColor;
    } else {
      return Constants.errorColor;
    }
  }
}

class ScooterListTile extends StatelessWidget {
  final Scooter scooter;
  final String? distance;
  final VoidCallback? onTap;
  final bool isSelected;

  const ScooterListTile({
    Key? key,
    required this.scooter,
    this.distance,
    this.onTap,
    this.isSelected = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: Constants.defaultPadding,
        vertical: Constants.smallPadding,
      ),
      color: isSelected ? Constants.primaryLightColor : null,
      child: ListTile(
        onTap: onTap,
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _getStatusColor(),
          ),
          child: Icon(
            Icons.electric_scooter,
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          'Scooter ${scooter.code}',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.battery_std,
                  size: 14,
                  color: _getBatteryColor(),
                ),
                const SizedBox(width: 4),
                Text(
                  '${scooter.batteryLevel}%',
                  style: TextStyle(
                    fontSize: Constants.bodySmallSize,
                    color: _getBatteryColor(),
                  ),
                ),
                const SizedBox(width: Constants.smallPadding),
                Text(
                  '\$${scooter.pricePerMinute.toStringAsFixed(2)}/min',
                  style: const TextStyle(
                    fontSize: Constants.bodySmallSize,
                    color: Constants.textSecondaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 2),
            Text(
              scooter.statusDisplayName,
              style: TextStyle(
                fontSize: Constants.bodySmallSize,
                color: _getStatusColor(),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        trailing: distance != null
            ? Text(
                distance!,
                style: const TextStyle(
                  fontSize: Constants.bodySmallSize,
                  color: Constants.textSecondaryColor,
                  fontWeight: FontWeight.w500,
                ),
              )
            : null,
      ),
    );
  }

  Color _getStatusColor() {
    switch (scooter.status) {
      case ScooterStatus.available:
        return Constants.scooterAvailableColor;
      case ScooterStatus.inUse:
        return Constants.scooterInUseColor;
      case ScooterStatus.maintenance:
        return Constants.scooterMaintenanceColor;
      case ScooterStatus.offline:
        return Constants.scooterOfflineColor;
    }
  }

  Color _getBatteryColor() {
    if (scooter.batteryLevel >= 50) {
      return Constants.successColor;
    } else if (scooter.batteryLevel >= 20) {
      return Constants.warningColor;
    } else {
      return Constants.errorColor;
    }
  }
}
