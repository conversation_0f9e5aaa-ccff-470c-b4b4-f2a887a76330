import 'dart:convert';
import 'dart:math';
import 'storage_service.dart';

class LocalDataService {
  static final LocalDataService _instance = LocalDataService._internal();
  factory LocalDataService() => _instance;
  LocalDataService._internal();

  final StorageService _storageService = StorageService();
  final Random _random = Random();

  // Mock users data
  static final List<Map<String, dynamic>> _mockUsers = [
    {
      'id': 1,
      'name': '<PERSON>',
      'email': '<EMAIL>',
      'password': 'password123',
      'phone': '+1234567890',
      'email_verified_at':
          DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
      'created_at':
          DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
      'updated_at':
          DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
    },
    {
      'id': 2,
      'name': '<PERSON>',
      'email': '<EMAIL>',
      'password': 'password123',
      'phone': '+1234567891',
      'email_verified_at':
          DateTime.now().subtract(const Duration(days: 15)).toIso8601String(),
      'created_at':
          DateTime.now().subtract(const Duration(days: 15)).toIso8601String(),
      'updated_at':
          DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
    },
    {
      'id': 3,
      'name': 'Demo User',
      'email': '<EMAIL>',
      'password': 'demo123',
      'phone': '+1234567892',
      'email_verified_at':
          DateTime.now().subtract(const Duration(days: 5)).toIso8601String(),
      'created_at':
          DateTime.now().subtract(const Duration(days: 5)).toIso8601String(),
      'updated_at': DateTime.now()
          .subtract(const Duration(minutes: 30))
          .toIso8601String(),
    },
  ];

  // Mock scooters data (around a central location)
  List<Map<String, dynamic>> _generateMockScooters() {
    final List<Map<String, dynamic>> scooters = [];
    const baseLatitude = 37.7749; // San Francisco
    const baseLongitude = -122.4194;

    for (int i = 1; i <= 20; i++) {
      // Generate random positions within ~2km radius
      final latOffset = (_random.nextDouble() - 0.5) * 0.02; // ~1km
      final lngOffset = (_random.nextDouble() - 0.5) * 0.02;

      scooters.add({
        'id': i,
        'code': 'SC${i.toString().padLeft(4, '0')}',
        'battery_level': _random.nextInt(80) + 20, // 20-100%
        'latitude': baseLatitude + latOffset,
        'longitude': baseLongitude + lngOffset,
        'is_available':
            _random.nextBool() || i <= 10, // Ensure at least 10 are available
        'last_maintenance': DateTime.now()
            .subtract(Duration(days: _random.nextInt(30)))
            .toIso8601String(),
        'created_at': DateTime.now()
            .subtract(Duration(days: _random.nextInt(365)))
            .toIso8601String(),
        'updated_at': DateTime.now()
            .subtract(Duration(minutes: _random.nextInt(60)))
            .toIso8601String(),
      });
    }

    return scooters;
  }

  // Authentication methods
  Future<Map<String, dynamic>> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    String? phone,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Check if passwords match
    if (password != passwordConfirmation) {
      throw LocalDataException(
        message: 'Passwords do not match',
        errors: {
          'password_confirmation': ['The password confirmation does not match.']
        },
      );
    }

    // Check if user already exists
    final existingUser = _mockUsers.firstWhere(
      (user) => user['email'] == email,
      orElse: () => {},
    );

    if (existingUser.isNotEmpty) {
      throw LocalDataException(
        message: 'User already exists',
        errors: {
          'email': ['The email has already been taken.']
        },
      );
    }

    // Create new user
    final newUser = {
      'id': _mockUsers.length + 1,
      'name': name,
      'email': email,
      'password': password,
      'phone': phone,
      'email_verified_at': DateTime.now().toIso8601String(),
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };

    _mockUsers.add(newUser);

    // Generate token
    final token = _generateToken(newUser['id'] as int);
    await _storageService.saveToken(token);

    return {
      'user': newUser,
      'token': token,
    };
  }

  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    final user = _mockUsers.firstWhere(
      (user) => user['email'] == email && user['password'] == password,
      orElse: () => {},
    );

    if (user.isEmpty) {
      throw LocalDataException(
        message: 'Invalid credentials',
        errors: {
          'email': ['These credentials do not match our records.']
        },
      );
    }

    // Generate token
    final token = _generateToken(user['id'] as int);
    await _storageService.saveToken(token);

    return {
      'user': user,
      'token': token,
    };
  }

  Future<Map<String, dynamic>> logout() async {
    await Future.delayed(const Duration(milliseconds: 200));
    await _storageService.deleteToken();
    return {'message': 'Successfully logged out'};
  }

  Future<Map<String, dynamic>> getProfile() async {
    await Future.delayed(const Duration(milliseconds: 300));

    final token = await _storageService.getToken();
    if (token == null) {
      throw LocalDataException(message: 'Unauthenticated');
    }

    final userId = _getUserIdFromToken(token);
    final user = _mockUsers.firstWhere(
      (user) => user['id'] == userId,
      orElse: () => {},
    );

    if (user.isEmpty) {
      throw LocalDataException(message: 'User not found');
    }

    return {'user': user};
  }

  // Scooter methods
  Future<Map<String, dynamic>> getScooters({
    double? latitude,
    double? longitude,
    double? radius,
  }) async {
    await Future.delayed(const Duration(milliseconds: 400));

    List<Map<String, dynamic>> scooters = _generateMockScooters();

    // Filter by location if provided
    if (latitude != null && longitude != null && radius != null) {
      scooters = scooters.where((scooter) {
        final distance = _calculateDistance(
          latitude,
          longitude,
          scooter['latitude'],
          scooter['longitude'],
        );
        return distance <= radius;
      }).toList();
    }

    return {'scooters': scooters};
  }

  Future<Map<String, dynamic>> getScooter(int scooterId) async {
    await Future.delayed(const Duration(milliseconds: 200));

    final scooters = _generateMockScooters();
    final scooter = scooters.firstWhere(
      (s) => s['id'] == scooterId,
      orElse: () => {},
    );

    if (scooter.isEmpty) {
      throw LocalDataException(message: 'Scooter not found');
    }

    return {'scooter': scooter};
  }

  // Ride methods
  Future<Map<String, dynamic>> startRide({
    required int scooterId,
    required double latitude,
    required double longitude,
  }) async {
    await Future.delayed(const Duration(milliseconds: 600));

    final token = await _storageService.getToken();
    if (token == null) {
      throw LocalDataException(message: 'Unauthenticated');
    }

    final userId = _getUserIdFromToken(token);

    // Check if scooter exists and is available
    final scooters = _generateMockScooters();
    final scooter = scooters.firstWhere(
      (s) => s['id'] == scooterId,
      orElse: () => {},
    );

    if (scooter.isEmpty) {
      throw LocalDataException(message: 'Scooter not found');
    }

    if (!scooter['is_available']) {
      throw LocalDataException(message: 'Scooter is not available');
    }

    // Create new ride
    final ride = {
      'id': _random.nextInt(10000) + 1000,
      'user_id': userId,
      'scooter_id': scooterId,
      'start_latitude': latitude,
      'start_longitude': longitude,
      'end_latitude': null,
      'end_longitude': null,
      'start_time': DateTime.now().toIso8601String(),
      'end_time': null,
      'total_cost': null,
      'duration_minutes': null,
      'status': 'active',
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
      'scooter_code': scooter['code'],
    };

    // Save current ride
    await _storageService.saveString('current_ride', json.encode(ride));

    return {'ride': ride};
  }

  Future<Map<String, dynamic>> endRide({
    required int rideId,
    required double latitude,
    required double longitude,
  }) async {
    await Future.delayed(const Duration(milliseconds: 600));

    final token = await _storageService.getToken();
    if (token == null) {
      throw LocalDataException(message: 'Unauthenticated');
    }

    // Get current ride
    final currentRideJson = await _storageService.getString('current_ride');
    if (currentRideJson == null) {
      throw LocalDataException(message: 'No active ride found');
    }

    final currentRide = json.decode(currentRideJson);
    if (currentRide['id'] != rideId) {
      throw LocalDataException(message: 'Ride not found');
    }

    // Calculate ride duration and cost
    final startTime = DateTime.parse(currentRide['start_time']);
    final endTime = DateTime.now();
    final durationMinutes = endTime.difference(startTime).inMinutes;
    final totalCost = durationMinutes * 0.25; // $0.25 per minute

    // Update ride
    currentRide['end_latitude'] = latitude;
    currentRide['end_longitude'] = longitude;
    currentRide['end_time'] = endTime.toIso8601String();
    currentRide['duration_minutes'] = durationMinutes;
    currentRide['total_cost'] = totalCost;
    currentRide['status'] = 'completed';
    currentRide['updated_at'] = endTime.toIso8601String();

    // Save to ride history and clear current ride
    await _saveRideToHistory(currentRide);
    await _storageService.saveString('current_ride', '');

    return {'ride': currentRide};
  }

  Future<Map<String, dynamic>> getCurrentRide() async {
    await Future.delayed(const Duration(milliseconds: 200));

    final token = await _storageService.getToken();
    if (token == null) {
      throw LocalDataException(message: 'Unauthenticated');
    }

    final currentRideJson = await _storageService.getString('current_ride');
    if (currentRideJson == null || currentRideJson.isEmpty) {
      return {'ride': null};
    }

    final currentRide = json.decode(currentRideJson);
    return {'ride': currentRide};
  }

  Future<Map<String, dynamic>> getRideHistory({
    int page = 1,
    int perPage = 20,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final token = await _storageService.getToken();
    if (token == null) {
      throw LocalDataException(message: 'Unauthenticated');
    }

    final userId = _getUserIdFromToken(token);
    final rideHistoryJson =
        await _storageService.getString('ride_history_$userId');

    List<dynamic> allRides = [];
    if (rideHistoryJson != null && rideHistoryJson.isNotEmpty) {
      allRides = json.decode(rideHistoryJson);
    }

    // Add some mock historical rides if none exist
    if (allRides.isEmpty) {
      allRides = _generateMockRideHistory(userId);
      await _storageService.saveString(
          'ride_history_$userId', json.encode(allRides));
    }

    // Sort by date (newest first)
    allRides.sort((a, b) => DateTime.parse(b['created_at'])
        .compareTo(DateTime.parse(a['created_at'])));

    // Paginate
    final startIndex = (page - 1) * perPage;
    final endIndex = startIndex + perPage;
    final paginatedRides = allRides.sublist(
      startIndex,
      endIndex > allRides.length ? allRides.length : endIndex,
    );

    return {
      'rides': paginatedRides,
      'total': allRides.length,
      'current_page': page,
      'per_page': perPage,
      'last_page': (allRides.length / perPage).ceil(),
    };
  }

  Future<Map<String, dynamic>> getRide(int rideId) async {
    await Future.delayed(const Duration(milliseconds: 200));

    final token = await _storageService.getToken();
    if (token == null) {
      throw LocalDataException(message: 'Unauthenticated');
    }

    final userId = _getUserIdFromToken(token);
    final rideHistoryJson =
        await _storageService.getString('ride_history_$userId');

    if (rideHistoryJson != null && rideHistoryJson.isNotEmpty) {
      final allRides = json.decode(rideHistoryJson) as List<dynamic>;
      final ride = allRides.firstWhere(
        (r) => r['id'] == rideId,
        orElse: () => {},
      );

      if (ride.isNotEmpty) {
        return {'ride': ride};
      }
    }

    throw LocalDataException(message: 'Ride not found');
  }

  Future<void> _saveRideToHistory(Map<String, dynamic> ride) async {
    final token = await _storageService.getToken();
    if (token == null) return;

    final userId = _getUserIdFromToken(token);
    final rideHistoryJson =
        await _storageService.getString('ride_history_$userId');

    List<dynamic> rideHistory = [];
    if (rideHistoryJson != null && rideHistoryJson.isNotEmpty) {
      rideHistory = json.decode(rideHistoryJson);
    }

    rideHistory.add(ride);
    await _storageService.saveString(
        'ride_history_$userId', json.encode(rideHistory));
  }

  List<Map<String, dynamic>> _generateMockRideHistory(int userId) {
    final List<Map<String, dynamic>> rides = [];

    for (int i = 0; i < 5; i++) {
      final startTime = DateTime.now()
          .subtract(Duration(days: i + 1, hours: _random.nextInt(12)));
      final durationMinutes = _random.nextInt(45) + 5; // 5-50 minutes
      final endTime = startTime.add(Duration(minutes: durationMinutes));
      final totalCost = durationMinutes * 0.25;

      rides.add({
        'id': 1000 + i,
        'user_id': userId,
        'scooter_id': _random.nextInt(20) + 1,
        'start_latitude': 37.7749 + (_random.nextDouble() - 0.5) * 0.02,
        'start_longitude': -122.4194 + (_random.nextDouble() - 0.5) * 0.02,
        'end_latitude': 37.7749 + (_random.nextDouble() - 0.5) * 0.02,
        'end_longitude': -122.4194 + (_random.nextDouble() - 0.5) * 0.02,
        'start_time': startTime.toIso8601String(),
        'end_time': endTime.toIso8601String(),
        'duration_minutes': durationMinutes,
        'total_cost': totalCost,
        'status': 'completed',
        'created_at': startTime.toIso8601String(),
        'updated_at': endTime.toIso8601String(),
        'scooter_code':
            'SC${(_random.nextInt(20) + 1).toString().padLeft(4, '0')}',
      });
    }

    return rides;
  }

  // Helper methods
  String _generateToken(int userId) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'local_token_${userId}_$timestamp';
  }

  int _getUserIdFromToken(String token) {
    final parts = token.split('_');
    return int.parse(parts[2]);
  }

  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371000; // meters
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);

    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }
}

class LocalDataException implements Exception {
  final String message;
  final Map<String, dynamic>? errors;

  LocalDataException({
    required this.message,
    this.errors,
  });

  @override
  String toString() {
    return 'LocalDataException: $message';
  }

  String get userFriendlyMessage => message;
}
