import 'package:geolocator/geolocator.dart';

class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  Position? _currentPosition;
  Position? get currentPosition => _currentPosition;

  Future<LocationResult> getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return LocationResult.failure(
          message: 'Location services are disabled. Please enable location services.',
        );
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return LocationResult.failure(
            message: 'Location permissions are denied. Please grant location access.',
          );
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return LocationResult.failure(
          message: 'Location permissions are permanently denied. Please enable them in settings.',
        );
      }

      // Get current position
      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      return LocationResult.success(position: _currentPosition!);
    } catch (e) {
      return LocationResult.failure(
        message: 'Failed to get current location: ${e.toString()}',
      );
    }
  }

  Future<LocationResult> getLocationWithTimeout({
    Duration timeout = const Duration(seconds: 15),
    LocationAccuracy accuracy = LocationAccuracy.high,
  }) async {
    try {
      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: accuracy,
        timeLimit: timeout,
      );

      return LocationResult.success(position: _currentPosition!);
    } catch (e) {
      return LocationResult.failure(
        message: 'Location request timed out. Please try again.',
      );
    }
  }

  Stream<Position> getLocationStream({
    LocationAccuracy accuracy = LocationAccuracy.high,
    int distanceFilter = 10,
  }) {
    const LocationSettings locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10,
    );

    return Geolocator.getPositionStream(locationSettings: locationSettings);
  }

  double calculateDistance({
    required double startLatitude,
    required double startLongitude,
    required double endLatitude,
    required double endLongitude,
  }) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  double calculateDistanceFromCurrent({
    required double latitude,
    required double longitude,
  }) {
    if (_currentPosition == null) {
      throw Exception('Current position not available');
    }

    return calculateDistance(
      startLatitude: _currentPosition!.latitude,
      startLongitude: _currentPosition!.longitude,
      endLatitude: latitude,
      endLongitude: longitude,
    );
  }

  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  Future<LocationPermission> checkPermission() async {
    return await Geolocator.checkPermission();
  }

  Future<LocationPermission> requestPermission() async {
    return await Geolocator.requestPermission();
  }

  Future<void> openLocationSettings() async {
    await Geolocator.openLocationSettings();
  }

  Future<void> openAppSettings() async {
    await Geolocator.openAppSettings();
  }

  String formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.round()}m';
    } else {
      return '${(distanceInMeters / 1000).toStringAsFixed(1)}km';
    }
  }

  bool isWithinRadius({
    required double centerLat,
    required double centerLng,
    required double targetLat,
    required double targetLng,
    required double radiusInMeters,
  }) {
    final distance = calculateDistance(
      startLatitude: centerLat,
      startLongitude: centerLng,
      endLatitude: targetLat,
      endLongitude: targetLng,
    );
    return distance <= radiusInMeters;
  }
}

class LocationResult {
  final bool isSuccess;
  final String message;
  final Position? position;

  LocationResult._({
    required this.isSuccess,
    required this.message,
    this.position,
  });

  factory LocationResult.success({required Position position}) {
    return LocationResult._(
      isSuccess: true,
      message: 'Location retrieved successfully',
      position: position,
    );
  }

  factory LocationResult.failure({required String message}) {
    return LocationResult._(
      isSuccess: false,
      message: message,
    );
  }
}
