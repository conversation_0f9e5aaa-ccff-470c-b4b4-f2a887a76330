import '../models/user.dart';
import 'local_data_service.dart';
import 'storage_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final LocalDataService _localDataService = LocalDataService();
  final StorageService _storageService = StorageService();

  User? _currentUser;
  User? get currentUser => _currentUser;

  bool get isLoggedIn => _currentUser != null;

  Future<bool> checkAuthStatus() async {
    try {
      final token = await _storageService.getToken();
      if (token == null) {
        return false;
      }

      final response = await _localDataService.getProfile();
      _currentUser = User.fromJson(response['user']);
      return true;
    } catch (e) {
      await logout();
      return false;
    }
  }

  Future<AuthResult> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    String? phone,
  }) async {
    try {
      final response = await _localDataService.register(
        name: name,
        email: email,
        password: password,
        passwordConfirmation: passwordConfirmation,
        phone: phone,
      );

      final token = response['token'];
      final user = User.fromJson(response['user']);

      await _storageService.saveToken(token);
      _currentUser = user;

      return AuthResult.success(user: user, message: 'Registration successful');
    } on LocalDataException catch (e) {
      return AuthResult.failure(
          message: e.userFriendlyMessage, errors: e.errors);
    } catch (e) {
      return AuthResult.failure(
          message: 'Registration failed. Please try again.');
    }
  }

  Future<AuthResult> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _localDataService.login(
        email: email,
        password: password,
      );

      final token = response['token'];
      final user = User.fromJson(response['user']);

      await _storageService.saveToken(token);
      _currentUser = user;

      return AuthResult.success(user: user, message: 'Login successful');
    } on LocalDataException catch (e) {
      return AuthResult.failure(
          message: e.userFriendlyMessage, errors: e.errors);
    } catch (e) {
      return AuthResult.failure(message: 'Login failed. Please try again.');
    }
  }

  Future<void> logout() async {
    try {
      await _localDataService.logout();
    } catch (e) {
      // Continue with logout even if API call fails
    } finally {
      await _storageService.deleteToken();
      _currentUser = null;
    }
  }

  Future<AuthResult> refreshProfile() async {
    try {
      final response = await _localDataService.getProfile();
      _currentUser = User.fromJson(response['user']);
      return AuthResult.success(
          user: _currentUser!, message: 'Profile updated');
    } on LocalDataException catch (e) {
      return AuthResult.failure(message: e.userFriendlyMessage);
    } catch (e) {
      return AuthResult.failure(message: 'Failed to refresh profile');
    }
  }
}

class AuthResult {
  final bool isSuccess;
  final String message;
  final User? user;
  final Map<String, dynamic>? errors;

  AuthResult._({
    required this.isSuccess,
    required this.message,
    this.user,
    this.errors,
  });

  factory AuthResult.success({
    required User user,
    required String message,
  }) {
    return AuthResult._(
      isSuccess: true,
      message: message,
      user: user,
    );
  }

  factory AuthResult.failure({
    required String message,
    Map<String, dynamic>? errors,
  }) {
    return AuthResult._(
      isSuccess: false,
      message: message,
      errors: errors,
    );
  }

  String? getFieldError(String field) {
    if (errors == null) return null;
    final fieldErrors = errors![field];
    if (fieldErrors is List && fieldErrors.isNotEmpty) {
      return fieldErrors.first.toString();
    }
    return null;
  }

  List<String> get allErrors {
    if (errors == null) return [message];

    final List<String> errorList = [];
    errors!.forEach((key, value) {
      if (value is List) {
        errorList.addAll(value.map((e) => e.toString()));
      } else {
        errorList.add(value.toString());
      }
    });

    return errorList.isEmpty ? [message] : errorList;
  }
}
