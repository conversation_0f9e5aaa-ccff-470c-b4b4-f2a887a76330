import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/scooters_provider.dart';
import '../providers/rides_provider.dart';
import '../utils/constants.dart';
import '../utils/routes.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/scooter_marker.dart';
import '../widgets/custom_button.dart';
import 'profile_drawer.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _showScooterList = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final scootersProvider = Provider.of<ScootersProvider>(context, listen: false);
    final ridesProvider = Provider.of<RidesProvider>(context, listen: false);
    
    await Future.wait([
      scootersProvider.loadScooters(),
      ridesProvider.loadCurrentRide(),
    ]);
  }

  Future<void> _refreshData() async {
    await _loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ScooterRide'),
        actions: [
          IconButton(
            icon: const Icon(Icons.qr_code_scanner),
            onPressed: () {
              AppRoutes.push(context, AppRoutes.qrScanner);
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
          ),
        ],
      ),
      drawer: const ProfileDrawer(),
      body: Consumer3<ScootersProvider, RidesProvider, AuthProvider>(
        builder: (context, scootersProvider, ridesProvider, authProvider, child) {
          if (scootersProvider.isLoading) {
            return const LoadingIndicator(message: 'Loading scooters...');
          }

          return Column(
            children: [
              // Current Ride Banner
              if (ridesProvider.hasActiveRide) ...[
                Container(
                  width: double.infinity,
                  color: Constants.accentColor,
                  padding: const EdgeInsets.all(Constants.defaultPadding),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.electric_scooter,
                        color: Constants.onPrimaryColor,
                      ),
                      const SizedBox(width: Constants.smallPadding),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Ride in Progress',
                              style: TextStyle(
                                color: Constants.onPrimaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'Duration: ${ridesProvider.getFormattedCurrentRideDuration()}',
                              style: const TextStyle(
                                color: Constants.onPrimaryColor,
                                fontSize: Constants.bodySmallSize,
                              ),
                            ),
                          ],
                        ),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          AppRoutes.push(context, AppRoutes.ride);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Constants.onPrimaryColor,
                          foregroundColor: Constants.accentColor,
                        ),
                        child: const Text('View Ride'),
                      ),
                    ],
                  ),
                ),
              ],
              
              // Map/List Toggle
              Container(
                padding: const EdgeInsets.all(Constants.smallPadding),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${scootersProvider.availableScooters.length} scooters available',
                        style: const TextStyle(
                          fontSize: Constants.bodyLargeSize,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: Icon(
                        _showScooterList ? Icons.map : Icons.list,
                      ),
                      onPressed: () {
                        setState(() {
                          _showScooterList = !_showScooterList;
                        });
                      },
                    ),
                  ],
                ),
              ),

              // Content Area
              Expanded(
                child: _showScooterList ? _buildScooterList() : _buildMapView(),
              ),
            ],
          );
        },
      ),
      floatingActionButton: Consumer<RidesProvider>(
        builder: (context, ridesProvider, child) {
          if (ridesProvider.hasActiveRide) {
            return FloatingActionButton.extended(
              onPressed: () {
                AppRoutes.push(context, AppRoutes.ride);
              },
              backgroundColor: Constants.accentColor,
              icon: const Icon(Icons.electric_scooter),
              label: const Text('Current Ride'),
            );
          }
          return FloatingActionButton(
            onPressed: () {
              AppRoutes.push(context, AppRoutes.qrScanner);
            },
            backgroundColor: Constants.primaryColor,
            child: const Icon(Icons.qr_code_scanner),
          );
        },
      ),
    );
  }

  Widget _buildMapView() {
    return Container(
      color: Constants.backgroundColor,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.map,
              size: 64,
              color: Constants.textSecondaryColor,
            ),
            SizedBox(height: Constants.defaultPadding),
            Text(
              'Map View',
              style: TextStyle(
                fontSize: Constants.titleLargeSize,
                color: Constants.textSecondaryColor,
              ),
            ),
            SizedBox(height: Constants.smallPadding),
            Text(
              'Google Maps integration would go here',
              style: TextStyle(
                fontSize: Constants.bodyMediumSize,
                color: Constants.textSecondaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScooterList() {
    return Consumer<ScootersProvider>(
      builder: (context, scootersProvider, child) {
        final scooters = scootersProvider.availableScooters;

        if (scooters.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.electric_scooter_outlined,
                  size: 64,
                  color: Constants.textSecondaryColor,
                ),
                SizedBox(height: Constants.defaultPadding),
                Text(
                  'No scooters available',
                  style: TextStyle(
                    fontSize: Constants.titleLargeSize,
                    color: Constants.textSecondaryColor,
                  ),
                ),
                SizedBox(height: Constants.smallPadding),
                Text(
                  'Try refreshing or check back later',
                  style: TextStyle(
                    fontSize: Constants.bodyMediumSize,
                    color: Constants.textSecondaryColor,
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _refreshData,
          child: ListView.builder(
            padding: const EdgeInsets.all(Constants.smallPadding),
            itemCount: scooters.length,
            itemBuilder: (context, index) {
              final scooter = scooters[index];
              final distance = scootersProvider.getFormattedDistanceToScooter(scooter);

              return ScooterListTile(
                scooter: scooter,
                distance: distance,
                onTap: () {
                  _showScooterDetails(scooter);
                },
              );
            },
          ),
        );
      },
    );
  }

  void _showScooterDetails(scooter) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Consumer2<ScootersProvider, RidesProvider>(
        builder: (context, scootersProvider, ridesProvider, child) {
          final distance = scootersProvider.getFormattedDistanceToScooter(scooter);

          return Container(
            padding: const EdgeInsets.all(Constants.defaultPadding),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ScooterInfoCard(
                  scooter: scooter,
                  distance: distance,
                  showActions: false,
                ),
                const SizedBox(height: Constants.defaultPadding),
                if (!ridesProvider.hasActiveRide && scooter.isAvailable)
                  CustomButton(
                    text: 'Start Ride',
                    onPressed: () async {
                      Navigator.pop(context);
                      await _startRide(scooter);
                    },
                    isLoading: ridesProvider.isLoading,
                  ),
                if (ridesProvider.hasActiveRide)
                  const Text(
                    'You already have an active ride',
                    style: TextStyle(
                      color: Constants.warningColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                const SizedBox(height: Constants.defaultPadding),
              ],
            ),
          );
        },
      ),
    );
  }

  Future<void> _startRide(scooter) async {
    final ridesProvider = Provider.of<RidesProvider>(context, listen: false);
    final scootersProvider = Provider.of<ScootersProvider>(context, listen: false);

    final success = await ridesProvider.startRide(scooter: scooter);

    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(Constants.rideStartedMessage),
          backgroundColor: Constants.successColor,
        ),
      );
      
      // Refresh scooters to update status
      await scootersProvider.refreshScooters();
      
      // Navigate to ride screen
      AppRoutes.push(context, AppRoutes.ride);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(ridesProvider.errorMessage ?? 'Failed to start ride'),
          backgroundColor: Constants.errorColor,
        ),
      );
    }
  }
}
