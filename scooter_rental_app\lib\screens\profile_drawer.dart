import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../utils/constants.dart';
import '../utils/routes.dart';

class ProfileDrawer extends StatelessWidget {
  const ProfileDrawer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          final user = authProvider.user;

          return Column(
            children: [
              // Header
              UserAccountsDrawerHeader(
                decoration: const BoxDecoration(
                  color: Constants.primaryColor,
                ),
                accountName: Text(
                  user?.name ?? 'User',
                  style: const TextStyle(
                    fontSize: Constants.titleLargeSize,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                accountEmail: Text(
                  user?.email ?? '',
                  style: const TextStyle(
                    fontSize: Constants.bodyMediumSize,
                  ),
                ),
                currentAccountPicture: CircleAvatar(
                  backgroundColor: Constants.onPrimaryColor,
                  child: Text(
                    user?.name?.substring(0, 1).toUpperCase() ?? 'U',
                    style: const TextStyle(
                      fontSize: Constants.titleLargeSize,
                      fontWeight: FontWeight.bold,
                      color: Constants.primaryColor,
                    ),
                  ),
                ),
              ),

              // Menu Items
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    ListTile(
                      leading: const Icon(Icons.history),
                      title: const Text('Ride History'),
                      onTap: () {
                        Navigator.pop(context);
                        AppRoutes.push(context, AppRoutes.rideHistory);
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.account_circle),
                      title: const Text('Profile'),
                      onTap: () {
                        Navigator.pop(context);
                        // TODO: Navigate to profile screen
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.payment),
                      title: const Text('Payment Methods'),
                      onTap: () {
                        Navigator.pop(context);
                        // TODO: Navigate to payment screen
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.settings),
                      title: const Text('Settings'),
                      onTap: () {
                        Navigator.pop(context);
                        // TODO: Navigate to settings screen
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.help),
                      title: const Text('Help & Support'),
                      onTap: () {
                        Navigator.pop(context);
                        // TODO: Navigate to help screen
                      },
                    ),
                    const Divider(),
                    ListTile(
                      leading: const Icon(Icons.info),
                      title: const Text('About'),
                      onTap: () {
                        Navigator.pop(context);
                        _showAboutDialog(context);
                      },
                    ),
                  ],
                ),
              ),

              // Logout
              Container(
                padding: const EdgeInsets.all(Constants.defaultPadding),
                child: ListTile(
                  leading: const Icon(
                    Icons.logout,
                    color: Constants.errorColor,
                  ),
                  title: const Text(
                    'Logout',
                    style: TextStyle(
                      color: Constants.errorColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _showLogoutDialog(context);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              final authProvider = Provider.of<AuthProvider>(context, listen: false);
              await authProvider.logout();
              if (context.mounted) {
                AppRoutes.pushAndRemoveUntil(context, AppRoutes.login);
              }
            },
            child: const Text(
              'Logout',
              style: TextStyle(color: Constants.errorColor),
            ),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: Constants.appName,
      applicationVersion: Constants.appVersion,
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: const BoxDecoration(
          color: Constants.primaryColor,
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.electric_scooter,
          size: 32,
          color: Constants.onPrimaryColor,
        ),
      ),
      children: [
        const Text('A modern scooter rental app built with Flutter.'),
        const SizedBox(height: 16),
        const Text('Features:'),
        const Text('• Find nearby scooters'),
        const Text('• QR code scanning'),
        const Text('• Real-time ride tracking'),
        const Text('• Ride history'),
        const Text('• Secure payments'),
      ],
    );
  }
}
