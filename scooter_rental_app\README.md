# ScooterRide - Flutter App (Offline Version)

A modern Flutter application for electric scooter rentals with offline functionality, local data storage, QR code scanning, and seamless ride management. This version works completely offline with mock data and local authentication.

## Features

- **Offline Authentication**: Local login and registration with mock users
- **Scooter Discovery**: Find nearby available scooters (mock data)
- **QR Code Scanning**: Quick scooter unlock via QR codes
- **Local Tracking**: Ride monitoring and location tracking (offline)
- **Ride Management**: Start, track, and end rides with local storage
- **Ride History**: View past rides and statistics (stored locally)
- **Local Data Storage**: All data stored securely on device
- **Profile Management**: User profile and settings (offline)

## Screenshots

*Screenshots would go here in a real project*

## Getting Started

### Prerequisites

- Flutter SDK (>=3.0.0)
- Dart SDK
- Android Studio / VS Code
- Android device or emulator
- iOS device or simulator (for iOS development)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd scooter_rental_app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure Google Maps (Optional)**
   - Get a Google Maps API key from Google Cloud Console
   - Add the API key to your platform-specific configuration files

4. **Run the app**
   ```bash
   flutter run
   ```

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── models/                   # Data models
│   ├── user.dart
│   ├── scooter.dart
│   └── ride.dart
├── screens/                  # UI screens
│   ├── splash_screen.dart
│   ├── auth/
│   │   ├── login_screen.dart
│   │   └── register_screen.dart
│   ├── home_screen.dart
│   ├── qr_scanner_screen.dart
│   ├── ride_screen.dart
│   ├── ride_history_screen.dart
│   └── profile_drawer.dart
├── services/                 # Business logic services
│   ├── local_data_service.dart
│   ├── auth_service.dart
│   ├── location_service.dart
│   └── storage_service.dart
├── providers/                # State management
│   ├── auth_provider.dart
│   ├── scooters_provider.dart
│   └── rides_provider.dart
├── widgets/                  # Reusable UI components
│   ├── custom_button.dart
│   ├── scooter_marker.dart
│   └── loading_indicator.dart
└── utils/                    # Utilities and constants
    ├── constants.dart
    └── routes.dart
```

## Dependencies

### Main Dependencies
- `provider`: State management
- `geolocator`: Location services
- `qr_code_scanner`: QR code scanning
- `google_maps_flutter`: Maps integration
- `flutter_secure_storage`: Secure local storage
- `permission_handler`: Runtime permissions

### Dev Dependencies
- `flutter_test`: Testing framework
- `flutter_lints`: Linting rules

## Configuration

### Mock Data
The app now uses local mock data instead of external APIs. Mock users are available for testing:
- Email: `<EMAIL>`, Password: `password123`
- Email: `<EMAIL>`, Password: `password123`
- Email: `<EMAIL>`, Password: `demo123`

### Permissions
The app requires the following permissions:
- Location access (for finding nearby scooters)
- Camera access (for QR code scanning)

These are automatically requested when needed.

## State Management

The app uses the Provider pattern for state management with three main providers:

- **AuthProvider**: Manages user authentication state
- **ScootersProvider**: Handles scooter data and location
- **RidesProvider**: Manages ride state and history

## Local Data Integration

The app now works completely offline with local data services:

- **Authentication**: Local login/registration with mock users
- **Scooters**: Mock scooter data generated around San Francisco coordinates
- **Rides**: Local ride management with secure storage
- **History**: Ride history stored locally on device
- **No Network Required**: All functionality works without internet connection

## Testing

Run tests using:
```bash
flutter test
```

## Building for Production

### Android
```bash
flutter build apk --release
# or for app bundle
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

## Troubleshooting

### Common Issues

1. **API Connection Issues**
   - Ensure the API URL is correct
   - Check network connectivity
   - Verify the Laravel API is running

2. **Location Permission Denied**
   - Grant location permissions in device settings
   - Ensure location services are enabled

3. **QR Scanner Not Working**
   - Grant camera permissions
   - Test on a physical device (camera doesn't work in emulator)

### Debug Mode
Run in debug mode for detailed error logs:
```bash
flutter run --debug
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Contact the development team

## Roadmap

- [ ] Push notifications for ride updates
- [ ] In-app payments integration
- [ ] Social features (ride sharing)
- [ ] Advanced analytics dashboard
- [ ] Multi-language support
- [ ] Dark mode theme
- [ ] Offline mode capabilities
