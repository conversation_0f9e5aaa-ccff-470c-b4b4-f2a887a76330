import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../utils/constants.dart';
import '../utils/routes.dart';
import '../widgets/loading_indicator.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _checkAuthAndNavigate();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
    ));

    _animationController.forward();
  }

  Future<void> _checkAuthAndNavigate() async {
    // Wait for animation to complete
    await Future.delayed(const Duration(milliseconds: 2500));

    if (!mounted) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    try {
      final isAuthenticated = await authProvider.checkAuthStatus();
      
      if (!mounted) return;

      if (isAuthenticated) {
        AppRoutes.pushAndRemoveUntil(context, AppRoutes.home);
      } else {
        AppRoutes.pushAndRemoveUntil(context, AppRoutes.login);
      }
    } catch (e) {
      if (!mounted) return;
      AppRoutes.pushAndRemoveUntil(context, AppRoutes.login);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Constants.primaryColor,
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: Column(
                        children: [
                          // App Logo/Icon
                          Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              color: Constants.onPrimaryColor,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.electric_scooter,
                              size: 60,
                              color: Constants.primaryColor,
                            ),
                          ),
                          const SizedBox(height: Constants.largePadding),
                          // App Name
                          Text(
                            Constants.appName,
                            style: const TextStyle(
                              fontSize: Constants.headlineLargeSize,
                              fontWeight: FontWeight.bold,
                              color: Constants.onPrimaryColor,
                              letterSpacing: 1.2,
                            ),
                          ),
                          const SizedBox(height: Constants.smallPadding),
                          // App Tagline
                          Text(
                            'Ride Smart, Ride Green',
                            style: TextStyle(
                              fontSize: Constants.bodyLargeSize,
                              color: Constants.onPrimaryColor.withOpacity(0.8),
                              fontWeight: FontWeight.w300,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(height: Constants.largePadding * 2),
              // Loading indicator
              FadeTransition(
                opacity: _fadeAnimation,
                child: const LoadingIndicator(
                  color: Constants.onPrimaryColor,
                  message: 'Loading...',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
